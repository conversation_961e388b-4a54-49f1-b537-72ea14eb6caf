# 提肛小助手 - 集成测试指南

## 测试环境准备

### 1. 后端测试
```bash
# 进入后端目录
cd backend

# 安装依赖（如果Python版本支持）
pip install -r requirements.txt

# 运行简单验证
python verify_logic.py

# 启动后端服务（如果环境支持）
python start.py
```

### 2. 前端测试
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 3. 桌面应用测试（需要Rust环境）
```bash
# 安装Tauri CLI
npm install -g @tauri-apps/cli

# 启动桌面应用开发模式
npm run tauri:dev

# 构建桌面应用
npm run tauri:build
```

## 功能测试清单

### ✅ 后端功能测试

#### 数据模型测试
- [ ] UserSettings 表结构正确
- [ ] ExerciseRecord 表结构正确
- [ ] DailyStats 表结构正确
- [ ] 数据库初始化正常

#### API接口测试
- [ ] GET /api/v1/settings - 获取用户设置
- [ ] POST /api/v1/settings - 更新用户设置
- [ ] GET /api/v1/stats - 获取统计数据
- [ ] POST /api/v1/exercise/start - 开始运动
- [ ] POST /api/v1/exercise/complete - 完成运动
- [ ] GET /api/v1/reminder/status - 获取提醒状态
- [ ] POST /api/v1/reminder/toggle - 切换提醒状态
- [ ] GET /health - 健康检查

#### 业务逻辑测试
- [ ] 提醒间隔验证（5-120分钟）
- [ ] 运动时长验证（3-30秒）
- [ ] 重复次数验证（5-50次）
- [ ] 工作时间检查逻辑
- [ ] 连续天数计算逻辑
- [ ] 统计数据计算准确性

### ✅ 前端功能测试

#### 页面路由测试
- [ ] 主页 (/) 正常显示
- [ ] 运动页面 (/exercise) 正常显示
- [ ] 设置页面 (/settings) 正常显示
- [ ] 统计页面 (/stats) 正常显示
- [ ] 页面间导航正常

#### 组件功能测试
- [ ] BreathingAnimation 呼吸动画正常
- [ ] ProgressAnimation 进度动画正常
- [ ] RewardAnimation 奖励动画正常
- [ ] 所有Element Plus组件正常显示

#### 状态管理测试
- [ ] settingsStore 设置状态管理
- [ ] exerciseStore 运动状态管理
- [ ] tauriStore 桌面应用状态管理
- [ ] 状态持久化正常

#### 用户交互测试
- [ ] 设置修改和保存
- [ ] 运动开始和完成流程
- [ ] 提醒开关切换
- [ ] 动画播放控制

### ✅ 桌面应用功能测试

#### 窗口管理测试
- [ ] 应用启动正常
- [ ] 窗口显示/隐藏
- [ ] 最小化/最大化
- [ ] 窗口关闭行为（隐藏到托盘）

#### 系统托盘测试
- [ ] 托盘图标显示
- [ ] 托盘菜单功能
- [ ] 左键点击显示窗口
- [ ] 右键菜单操作

#### 通知系统测试
- [ ] 系统通知显示
- [ ] 提醒通知内容正确
- [ ] 通知权限请求
- [ ] 跨平台通知兼容性

#### 快捷键测试
- [ ] 全局快捷键注册（Ctrl+Shift+K）
- [ ] 快捷键响应正常
- [ ] 快捷键冲突处理

### ✅ 动画效果测试

#### 呼吸动画测试
- [ ] 圆圈缩放动画流畅
- [ ] 吸气/呼气状态切换
- [ ] 动画时长准确（4秒周期）
- [ ] 粒子效果正常
- [ ] 响应式适配

#### 进度动画测试
- [ ] 圆形进度条动画
- [ ] 线性进度条动画
- [ ] 步骤指示器动画
- [ ] 完成动画效果
- [ ] 动画性能优化

#### 奖励动画测试
- [ ] 庆祝粒子效果
- [ ] 图标弹跳动画
- [ ] 文字渐入效果
- [ ] 不同奖励类型显示
- [ ] 自动关闭功能

### ✅ 性能测试

#### 前端性能
- [ ] 页面加载速度
- [ ] 动画帧率稳定
- [ ] 内存使用合理
- [ ] 组件渲染优化

#### 后端性能
- [ ] API响应时间
- [ ] 数据库查询效率
- [ ] 内存使用监控
- [ ] 并发处理能力

#### 桌面应用性能
- [ ] 应用启动时间
- [ ] 内存占用
- [ ] CPU使用率
- [ ] 系统资源占用

### ✅ 兼容性测试

#### 浏览器兼容性
- [ ] Chrome 最新版本
- [ ] Firefox 最新版本
- [ ] Safari 最新版本
- [ ] Edge 最新版本

#### 操作系统兼容性
- [ ] Windows 10/11
- [ ] macOS 最新版本
- [ ] Linux 主流发行版

#### 设备兼容性
- [ ] 桌面设备（1920x1080及以上）
- [ ] 笔记本设备（1366x768及以上）
- [ ] 高分辨率显示器
- [ ] 多显示器支持

## 问题记录和解决方案

### 已知问题
1. **Python环境兼容性**
   - 问题：当前环境Python版本较低，无法安装最新FastAPI
   - 解决方案：升级Python到3.8+或使用虚拟环境

2. **Tauri依赖**
   - 问题：需要Rust环境支持
   - 解决方案：安装Rust工具链和Tauri CLI

3. **图标资源**
   - 问题：缺少应用图标文件
   - 解决方案：创建或获取合适的图标文件

### 性能优化建议
1. **前端优化**
   - 使用Vue3的Composition API优化组件性能
   - 实现虚拟滚动（如果有长列表）
   - 优化动画性能，使用CSS3硬件加速

2. **后端优化**
   - 实现数据库连接池
   - 添加API缓存机制
   - 优化数据库查询语句

3. **桌面应用优化**
   - 减少Tauri bundle大小
   - 优化系统资源使用
   - 实现懒加载机制

## 测试报告模板

### 测试环境
- 操作系统：
- 浏览器版本：
- Python版本：
- Node.js版本：
- Rust版本：

### 测试结果
- 通过测试项：X/Y
- 失败测试项：列出具体项目
- 性能指标：响应时间、内存使用等

### 问题总结
- 严重问题：
- 一般问题：
- 建议改进：

### 结论
- [ ] 可以发布
- [ ] 需要修复后发布
- [ ] 需要重大修改
