{"build": {"beforeDevCommand": "cd frontend && npm run dev", "beforeBuildCommand": "cd frontend && npm run build", "devPath": "http://localhost:3000", "distDir": "../frontend/dist", "withGlobalTauri": false}, "package": {"productName": "提肛小助手", "version": "1.0.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}, "window": {"all": false, "close": true, "hide": true, "show": true, "maximize": true, "minimize": true, "unmaximize": true, "unminimize": true, "startDragging": true, "setTitle": true, "setSize": true, "setPosition": true, "setFocus": true, "center": true}, "notification": {"all": true}, "globalShortcut": {"all": true}, "systemTray": {"all": true}, "app": {"all": false, "show": true, "hide": true}, "fs": {"all": false, "readFile": true, "writeFile": true, "readDir": false, "copyFile": false, "createDir": false, "removeDir": false, "removeFile": false, "renameFile": false, "exists": true}, "path": {"all": true}, "os": {"all": true}}, "bundle": {"active": true, "targets": "all", "identifier": "com.kegelhelper.app", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "security": {"csp": null}, "windows": [{"fullscreen": false, "resizable": true, "title": "提肛小助手", "width": 1000, "height": 700, "minWidth": 800, "minHeight": 600, "center": true, "decorations": true, "alwaysOnTop": false, "skipTaskbar": false, "visible": true}], "systemTray": {"iconPath": "icons/icon.png", "iconAsTemplate": true, "menuOnLeftClick": false, "tooltip": "提肛小助手"}}}