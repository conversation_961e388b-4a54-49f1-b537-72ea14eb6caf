{"name": "kegel-helper", "version": "1.0.0", "description": "现代化的提肛运动辅助软件", "main": "index.js", "scripts": {"dev": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "backend:dev": "cd backend && python start.py", "backend:install": "cd backend && pip install -r requirements.txt", "frontend:install": "cd frontend && npm install", "install:all": "npm run frontend:install && npm run backend:install", "start": "concurrently \"npm run backend:dev\" \"npm run dev\"", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["health", "exercise", "kegel", "desktop", "tauri", "vue"], "author": "Kegel Helper Team", "license": "MIT", "devDependencies": {"@tauri-apps/cli": "^1.5.0", "concurrently": "^8.2.2"}, "repository": {"type": "git", "url": "https://github.com/kegel-helper/kegel-helper.git"}, "bugs": {"url": "https://github.com/kegel-helper/kegel-helper/issues"}, "homepage": "https://github.com/kegel-helper/kegel-helper#readme"}