[package]
name = "kegel-helper"
version = "1.0.0"
description = "现代化的提肛运动辅助软件"
authors = ["Kegel Helper Team"]
license = "MIT"
repository = "https://github.com/kegel-helper/kegel-helper"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.5", features = [] }

[dependencies]
tauri = { version = "1.5", features = [
    "api-all",
    "icon-ico",
    "icon-png",
    "system-tray",
    "notification-all",
    "global-shortcut-all",
    "window-all",
    "shell-open"
] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1", features = ["full"] }
reqwest = { version = "0.11", features = ["json"] }
chrono = { version = "0.4", features = ["serde"] }

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
