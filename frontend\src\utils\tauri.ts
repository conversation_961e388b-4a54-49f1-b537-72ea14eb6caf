/**
 * Tauri API 集成工具
 * 提供与Tauri后端的交互接口
 */

// 检查是否在Tauri环境中运行
export const isTauri = () => {
  return typeof window !== 'undefined' && window.__TAURI__ !== undefined
}

// 动态导入Tauri API
const getTauriApi = async () => {
  if (!isTauri()) {
    throw new Error('Not running in Tauri environment')
  }
  
  const { invoke } = await import('@tauri-apps/api/tauri')
  const { sendNotification, isPermissionGranted, requestPermission } = await import('@tauri-apps/api/notification')
  const { register, unregister } = await import('@tauri-apps/api/globalShortcut')
  const { appWindow } = await import('@tauri-apps/api/window')
  
  return {
    invoke,
    sendNotification,
    isPermissionGranted,
    requestPermission,
    register,
    unregister,
    appWindow
  }
}

// 通知相关
export const showNotification = async (title: string, body: string) => {
  try {
    if (isTauri()) {
      const { invoke } = await getTauriApi()
      await invoke('show_notification', { title, body })
    } else {
      // 浏览器环境的备选方案
      if ('Notification' in window) {
        if (Notification.permission === 'granted') {
          new Notification(title, { body })
        } else if (Notification.permission !== 'denied') {
          const permission = await Notification.requestPermission()
          if (permission === 'granted') {
            new Notification(title, { body })
          }
        }
      }
    }
  } catch (error) {
    console.error('显示通知失败:', error)
  }
}

// 提醒相关
export const toggleReminder = async (): Promise<boolean> => {
  try {
    if (isTauri()) {
      const { invoke } = await getTauriApi()
      return await invoke('toggle_reminder')
    }
    return false
  } catch (error) {
    console.error('切换提醒状态失败:', error)
    return false
  }
}

export const getReminderStatus = async (): Promise<boolean> => {
  try {
    if (isTauri()) {
      const { invoke } = await getTauriApi()
      return await invoke('get_reminder_status')
    }
    return false
  } catch (error) {
    console.error('获取提醒状态失败:', error)
    return false
  }
}

export const sendReminderNotification = async () => {
  try {
    if (isTauri()) {
      const { invoke } = await getTauriApi()
      await invoke('send_reminder_notification')
    } else {
      await showNotification('提肛小助手', '该做提肛运动了！保持健康从现在开始 💪')
    }
  } catch (error) {
    console.error('发送提醒通知失败:', error)
  }
}

// 窗口相关
export const minimizeToTray = async () => {
  try {
    if (isTauri()) {
      const { invoke } = await getTauriApi()
      await invoke('minimize_to_tray')
    }
  } catch (error) {
    console.error('最小化到托盘失败:', error)
  }
}

export const showFromTray = async () => {
  try {
    if (isTauri()) {
      const { invoke } = await getTauriApi()
      await invoke('show_from_tray')
    }
  } catch (error) {
    console.error('从托盘显示失败:', error)
  }
}

// 快捷键相关
export const registerGlobalShortcut = async () => {
  try {
    if (isTauri()) {
      const { invoke } = await getTauriApi()
      await invoke('register_global_shortcut')
    }
  } catch (error) {
    console.error('注册全局快捷键失败:', error)
  }
}

// 窗口控制
export const hideWindow = async () => {
  try {
    if (isTauri()) {
      const { appWindow } = await getTauriApi()
      await appWindow.hide()
    }
  } catch (error) {
    console.error('隐藏窗口失败:', error)
  }
}

export const showWindow = async () => {
  try {
    if (isTauri()) {
      const { appWindow } = await getTauriApi()
      await appWindow.show()
      await appWindow.setFocus()
    }
  } catch (error) {
    console.error('显示窗口失败:', error)
  }
}

export const minimizeWindow = async () => {
  try {
    if (isTauri()) {
      const { appWindow } = await getTauriApi()
      await appWindow.minimize()
    }
  } catch (error) {
    console.error('最小化窗口失败:', error)
  }
}

export const maximizeWindow = async () => {
  try {
    if (isTauri()) {
      const { appWindow } = await getTauriApi()
      await appWindow.toggleMaximize()
    }
  } catch (error) {
    console.error('最大化窗口失败:', error)
  }
}

export const closeWindow = async () => {
  try {
    if (isTauri()) {
      const { appWindow } = await getTauriApi()
      await appWindow.close()
    }
  } catch (error) {
    console.error('关闭窗口失败:', error)
  }
}

// 系统信息
export const getSystemInfo = async () => {
  try {
    if (isTauri()) {
      const { platform, version, type, arch } = await import('@tauri-apps/api/os')
      return {
        platform: await platform(),
        version: await version(),
        type: await type(),
        arch: await arch()
      }
    }
    return null
  } catch (error) {
    console.error('获取系统信息失败:', error)
    return null
  }
}

// 应用信息
export const getAppInfo = async () => {
  try {
    if (isTauri()) {
      const { getName, getVersion } = await import('@tauri-apps/api/app')
      return {
        name: await getName(),
        version: await getVersion()
      }
    }
    return null
  } catch (error) {
    console.error('获取应用信息失败:', error)
    return null
  }
}

// 导出所有API
export default {
  isTauri,
  showNotification,
  toggleReminder,
  getReminderStatus,
  sendReminderNotification,
  minimizeToTray,
  showFromTray,
  registerGlobalShortcut,
  hideWindow,
  showWindow,
  minimizeWindow,
  maximizeWindow,
  closeWindow,
  getSystemInfo,
  getAppInfo
}
