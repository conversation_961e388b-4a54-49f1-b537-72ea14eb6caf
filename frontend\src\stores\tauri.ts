import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import * as tauriApi from '@/utils/tauri'

export const useTauriStore = defineStore('tauri', () => {
  // 状态
  const isDesktop = ref(tauriApi.isTauri())
  const reminderEnabled = ref(false)
  const systemInfo = ref<any>(null)
  const appInfo = ref<any>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const canUseDesktopFeatures = computed(() => isDesktop.value)
  
  const platformInfo = computed(() => {
    if (!systemInfo.value) return '未知平台'
    return `${systemInfo.value.platform} ${systemInfo.value.version}`
  })

  // 动作
  const initializeTauri = async () => {
    if (!isDesktop.value) return

    try {
      loading.value = true
      error.value = null

      // 获取系统信息
      systemInfo.value = await tauriApi.getSystemInfo()
      
      // 获取应用信息
      appInfo.value = await tauriApi.getAppInfo()
      
      // 获取提醒状态
      reminderEnabled.value = await tauriApi.getReminderStatus()
      
      // 注册全局快捷键
      await tauriApi.registerGlobalShortcut()
      
      console.log('Tauri 初始化完成')
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Tauri 初始化失败'
      console.error('Tauri 初始化失败:', err)
    } finally {
      loading.value = false
    }
  }

  const showNotification = async (title: string, message: string) => {
    try {
      await tauriApi.showNotification(title, message)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '显示通知失败'
      console.error('显示通知失败:', err)
    }
  }

  const toggleReminder = async () => {
    try {
      loading.value = true
      error.value = null
      
      const newStatus = await tauriApi.toggleReminder()
      reminderEnabled.value = newStatus
      
      const message = newStatus ? '提醒已开启' : '提醒已关闭'
      await showNotification('提肛小助手', message)
      
      return newStatus
    } catch (err) {
      error.value = err instanceof Error ? err.message : '切换提醒失败'
      console.error('切换提醒失败:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  const sendTestReminder = async () => {
    try {
      await tauriApi.sendReminderNotification()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '发送测试提醒失败'
      console.error('发送测试提醒失败:', err)
    }
  }

  const minimizeToTray = async () => {
    try {
      await tauriApi.minimizeToTray()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '最小化到托盘失败'
      console.error('最小化到托盘失败:', err)
    }
  }

  const showFromTray = async () => {
    try {
      await tauriApi.showFromTray()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '从托盘显示失败'
      console.error('从托盘显示失败:', err)
    }
  }

  const hideWindow = async () => {
    try {
      await tauriApi.hideWindow()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '隐藏窗口失败'
      console.error('隐藏窗口失败:', err)
    }
  }

  const showWindow = async () => {
    try {
      await tauriApi.showWindow()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '显示窗口失败'
      console.error('显示窗口失败:', err)
    }
  }

  const minimizeWindow = async () => {
    try {
      await tauriApi.minimizeWindow()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '最小化窗口失败'
      console.error('最小化窗口失败:', err)
    }
  }

  const maximizeWindow = async () => {
    try {
      await tauriApi.maximizeWindow()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '最大化窗口失败'
      console.error('最大化窗口失败:', err)
    }
  }

  const closeWindow = async () => {
    try {
      await tauriApi.closeWindow()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '关闭窗口失败'
      console.error('关闭窗口失败:', err)
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    isDesktop,
    reminderEnabled,
    systemInfo,
    appInfo,
    loading,
    error,
    
    // 计算属性
    canUseDesktopFeatures,
    platformInfo,
    
    // 动作
    initializeTauri,
    showNotification,
    toggleReminder,
    sendTestReminder,
    minimizeToTray,
    showFromTray,
    hideWindow,
    showWindow,
    minimizeWindow,
    maximizeWindow,
    closeWindow,
    clearError
  }
})
