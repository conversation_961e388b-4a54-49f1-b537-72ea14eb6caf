// Mock Tauri API for development
export const invoke = async (command: string, args?: any) => {
  console.log(`Mock Tauri invoke: ${command}`, args)
  
  // Mock responses for different commands
  switch (command) {
    case 'toggle_reminder':
      return true
    case 'get_reminder_status':
      return false
    case 'show_notification':
      return true
    case 'minimize_to_tray':
      return true
    case 'show_from_tray':
      return true
    case 'register_global_shortcut':
      return true
    default:
      return true
  }
}
