{"name": "@tauri-apps/cli-win32-x64-msvc", "version": "2.7.1", "cpu": ["x64"], "main": "cli.win32-x64-msvc.node", "files": ["cli.win32-x64-msvc.node"], "description": "Command line interface for building Tauri apps", "homepage": "https://github.com/tauri-apps/tauri#readme", "contributors": ["Tauri Programme within The Commons Conservancy"], "license": "Apache-2.0 OR MIT", "engines": {"node": ">= 10"}, "repository": {"type": "git", "url": "git+https://github.com/tauri-apps/tauri.git"}, "bugs": {"url": "https://github.com/tauri-apps/tauri/issues"}, "publishConfig": {"access": "public"}, "os": ["win32"]}