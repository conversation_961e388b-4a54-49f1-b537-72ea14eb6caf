# 提肛小助手 - 部署指南

## 项目概述

提肛小助手是一款现代化的PC端提肛运动辅助软件，采用以下技术栈：

- **后端**: Python + FastAPI + SQLAlchemy + APScheduler
- **前端**: Vue3 + TypeScript + Element Plus + Vite
- **桌面化**: Tauri (Rust)
- **动画**: CSS3 + 自定义动画组件

## 环境要求

### 开发环境
- **Python**: 3.8+ (推荐 3.9+)
- **Node.js**: 16+ (推荐 18+)
- **Rust**: 1.70+ (仅桌面应用需要)
- **Git**: 最新版本

### 生产环境
- **操作系统**: Windows 10+, macOS 10.15+, Linux (Ubuntu 20.04+)
- **内存**: 最低 4GB，推荐 8GB+
- **存储**: 最低 500MB 可用空间

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd tg-helper
```

### 2. 安装依赖

#### 前端依赖
```bash
cd frontend
npm install
```

#### 后端依赖
```bash
cd backend
pip install -r requirements.txt
```

#### 桌面应用依赖（可选）
```bash
# 安装 Tauri CLI
npm install -g @tauri-apps/cli

# 或使用项目根目录的脚本
npm install
```

### 3. 开发模式启动

#### 仅前端开发
```bash
cd frontend
npm run dev
```
访问: http://localhost:3000

#### 前端 + 后端
```bash
# 终端1: 启动后端
cd backend
python start.py

# 终端2: 启动前端
cd frontend
npm run dev
```

#### 桌面应用开发
```bash
# 在项目根目录
npm run tauri:dev
```

## 生产部署

### 方案1: Web应用部署

#### 前端构建
```bash
cd frontend
npm run build
```
构建产物在 `frontend/dist` 目录

#### 后端部署
```bash
cd backend
# 使用 gunicorn 或 uvicorn 部署
uvicorn main:app --host 0.0.0.0 --port 8000
```

#### Nginx 配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /path/to/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # 后端API代理
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 方案2: 桌面应用部署

#### 构建桌面应用
```bash
# 在项目根目录
npm run tauri:build
```

构建产物位置：
- **Windows**: `src-tauri/target/release/bundle/msi/`
- **macOS**: `src-tauri/target/release/bundle/dmg/`
- **Linux**: `src-tauri/target/release/bundle/deb/` 或 `appimage/`

#### 分发方式
1. **直接分发**: 将构建的安装包直接分发给用户
2. **应用商店**: 发布到 Microsoft Store, Mac App Store 等
3. **自动更新**: 集成 Tauri 的自动更新功能

## Docker 部署（推荐）

### Dockerfile 示例

#### 后端 Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY backend/requirements.txt .
RUN pip install -r requirements.txt

COPY backend/ .
EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 前端 Dockerfile
```dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY frontend/package*.json ./
RUN npm install

COPY frontend/ .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
```

#### Docker Compose
```yaml
version: '3.8'
services:
  backend:
    build: 
      context: .
      dockerfile: backend/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./data/kegel_helper.db
    volumes:
      - ./data:/app/data

  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend
```

### 启动服务
```bash
docker-compose up -d
```

## 配置说明

### 环境变量

#### 后端配置 (.env)
```env
# 基础配置
DEBUG=false
HOST=127.0.0.1
PORT=8000

# 数据库配置
DATABASE_URL=sqlite:///./kegel_helper.db

# 安全配置
SECRET_KEY=your-secret-key-here

# 提醒配置
DEFAULT_REMINDER_INTERVAL=30
DEFAULT_EXERCISE_DURATION=5
DEFAULT_EXERCISE_REPETITIONS=10

# 系统功能
ENABLE_SYSTEM_TRAY=true
ENABLE_NOTIFICATIONS=true
```

#### 前端配置
```typescript
// frontend/src/utils/config.ts
export const config = {
  apiBaseUrl: process.env.NODE_ENV === 'production' 
    ? '/api/v1' 
    : 'http://localhost:8000/api/v1',
  enableDevTools: process.env.NODE_ENV === 'development'
}
```

### Tauri 配置
主要配置在 `src-tauri/tauri.conf.json`:
- 窗口大小和行为
- 系统托盘设置
- 权限配置
- 构建选项

## 监控和日志

### 日志配置
```python
# backend/utils/logging.py
import logging
from logging.handlers import RotatingFileHandler

def setup_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            RotatingFileHandler('logs/app.log', maxBytes=10485760, backupCount=5),
            logging.StreamHandler()
        ]
    )
```

### 健康检查
- **后端**: GET `/health`
- **前端**: 检查页面加载状态
- **桌面应用**: 进程监控

## 故障排除

### 常见问题

#### 1. Python 依赖安装失败
```bash
# 升级 pip
pip install --upgrade pip

# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

#### 2. Node.js 依赖安装失败
```bash
# 清理缓存
npm cache clean --force

# 使用 yarn 替代
yarn install
```

#### 3. Tauri 构建失败
```bash
# 更新 Rust
rustup update

# 清理构建缓存
cargo clean
```

#### 4. 数据库连接问题
- 检查数据库文件权限
- 确认数据库路径正确
- 查看数据库初始化日志

### 性能优化

#### 前端优化
- 启用 gzip 压缩
- 使用 CDN 加速静态资源
- 实现代码分割和懒加载

#### 后端优化
- 使用数据库连接池
- 实现 API 缓存
- 优化数据库查询

#### 桌面应用优化
- 减少 bundle 大小
- 优化启动时间
- 实现增量更新

## 安全考虑

### 数据安全
- 用户数据本地存储
- 敏感信息加密
- 定期数据备份

### 应用安全
- 输入验证和过滤
- API 访问控制
- 安全的更新机制

## 维护和更新

### 版本管理
- 使用语义化版本号
- 维护更新日志
- 提供迁移脚本

### 自动更新
- Tauri 内置更新机制
- 增量更新支持
- 回滚机制

### 用户支持
- 错误报告收集
- 用户反馈渠道
- 使用统计分析

## 许可证和法律

- 项目采用 MIT 许可证
- 遵守相关隐私法规
- 第三方依赖许可证兼容性检查
