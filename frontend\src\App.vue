<template>
  <div id="app">
    <router-view />

    <!-- 桌面应用特有的标题栏（如果需要自定义） -->
    <div v-if="tauriStore.isDesktop" class="desktop-titlebar" data-tauri-drag-region>
      <!-- 可以在这里添加自定义标题栏 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useTauriStore } from '@/stores/tauri'

const tauriStore = useTauriStore()

// 初始化Tauri功能
onMounted(async () => {
  if (tauriStore.isDesktop) {
    await tauriStore.initializeTauri()
  }
})
</script>

<style>
#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  height: 100vh;
  margin: 0;
  padding: 0;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}
</style>
