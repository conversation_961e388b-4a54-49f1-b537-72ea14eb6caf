/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'lottie-web' {
  export interface AnimationItem {
    play(): void
    pause(): void
    stop(): void
    destroy(): void
    setSpeed(speed: number): void
    setDirection(direction: number): void
    goToAndStop(value: number, isFrame?: boolean): void
    goToAndPlay(value: number, isFrame?: boolean): void
  }

  export interface AnimationConfig {
    container: Element
    renderer: 'svg' | 'canvas' | 'html'
    loop?: boolean
    autoplay?: boolean
    path?: string
    animationData?: any
  }

  export function loadAnimation(config: AnimationConfig): AnimationItem
  export function destroy(): void
  export function registerAnimation(): void
  export function setQuality(quality: string | number): void
}
